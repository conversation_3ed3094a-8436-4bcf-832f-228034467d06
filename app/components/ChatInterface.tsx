'use client';

import { useChat } from '@ai-sdk/react';
import { useConversations } from '@/app/context/ConversationContext';
import { useEffect, useRef, useCallback } from 'react';
import { Message } from 'ai';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github.css';

export default function ChatInterface() {
  const { 
    currentConversation, 
    currentConversationId, 
    updateConversation,
    createNewConversation 
  } = useConversations();

  const isInitializing = useRef(false);
  const lastSyncedConversationId = useRef<string | null>(null);
  const lastSyncedMessagesLength = useRef(0);
  const isTransitioning = useRef(false);

  const { messages, input, handleInputChange, handleSubmit, isLoading, setMessages } = useChat({
    api: '/api/chat'
  });

  // Initialize conversation if none exists
  useEffect(() => {
    // Only create a new conversation if we truly have no current conversation
    // and we're not in the middle of switching or initializing
    if (!currentConversationId &&
        !currentConversation &&
        !isInitializing.current &&
        !isTransitioning.current &&
        lastSyncedConversationId.current === null) {
      isInitializing.current = true;
      console.log('No conversation exists, creating new one');
      // Clear any existing messages before creating new conversation
      setMessages([]);
      lastSyncedMessagesLength.current = 0;
      lastSyncedConversationId.current = null;
      createNewConversation();
    }
  }, [currentConversationId, currentConversation, createNewConversation, setMessages]);

  // Load messages when conversation changes (but prevent circular updates)
  useEffect(() => {
    if (currentConversationId && currentConversationId !== lastSyncedConversationId.current) {
      console.log('Loading conversation:', currentConversationId, 'Previous:', lastSyncedConversationId.current);

      // Mark as transitioning to prevent updates during switch
      isTransitioning.current = true;

      // Clear messages immediately to prevent carryover
      setMessages([]);
      lastSyncedMessagesLength.current = 0;

      // Update tracking
      lastSyncedConversationId.current = currentConversationId;
      isInitializing.current = false;

      // Load messages from the conversation immediately
      if (currentConversation && currentConversation.messages.length > 0) {
        console.log('Loading', currentConversation.messages.length, 'messages for conversation', currentConversationId);
        const conversationMessages: Message[] = currentConversation.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          createdAt: msg.createdAt
        }));
        setMessages(conversationMessages);
        lastSyncedMessagesLength.current = currentConversation.messages.length;
      } else {
        // New conversation - ensure messages stay cleared
        console.log('New conversation - ensuring messages are cleared');
        setMessages([]);
        lastSyncedMessagesLength.current = 0;
      }

      // Mark transition as complete after a brief delay to prevent immediate updates
      setTimeout(() => {
        isTransitioning.current = false;
      }, 100);
    }
  }, [currentConversationId, currentConversation, setMessages]);

  // Handle case where conversation ID exists but conversation object doesn't
  useEffect(() => {
    if (currentConversationId && !currentConversation && !isTransitioning.current) {
      console.log('Conversation ID exists but no conversation object found, clearing state');
      setMessages([]);
      lastSyncedMessagesLength.current = 0;
    }
  }, [currentConversationId, currentConversation, setMessages]);

  // Update function to save messages to conversation
  const debouncedUpdateConversation = useCallback((id: string, msgs: Message[]) => {
    // Don't update during transitions or initialization
    if (isInitializing.current || isTransitioning.current) {
      console.log('Skipping update during transition/initialization');
      return;
    }

    // Ensure we're updating the correct conversation
    if (id !== currentConversationId) {
      console.warn('Attempted to update conversation that is not current:', id, 'vs', currentConversationId);
      return;
    }

    // Ensure we're synced to the right conversation
    if (id !== lastSyncedConversationId.current) {
      console.warn('Attempted to update conversation that is not synced:', id, 'vs', lastSyncedConversationId.current);
      return;
    }

    // Only update if we have messages and they're different from what we last synced
    if (msgs.length > 0 && msgs.length !== lastSyncedMessagesLength.current) {
      console.log('Updating conversation', id, 'with', msgs.length, 'messages (was', lastSyncedMessagesLength.current, ')');

      lastSyncedMessagesLength.current = msgs.length;

      const formattedMessages = msgs.map(msg => ({
        id: msg.id,
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        createdAt: msg.createdAt || new Date()
      }));

      updateConversation(id, formattedMessages);
    }
  }, [updateConversation, currentConversationId]);

  // Update conversation when messages change
  useEffect(() => {
    if (currentConversationId &&
        currentConversationId === lastSyncedConversationId.current &&
        !isTransitioning.current &&
        !isInitializing.current) {
      debouncedUpdateConversation(currentConversationId, messages);
    }
  }, [messages, currentConversationId, debouncedUpdateConversation]);

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    // Ensure we have a conversation before submitting
    if (!currentConversationId) {
      createNewConversation();
    }
    handleSubmit(e);
  };

  // Handle new conversation creation - ensure clean state
  useEffect(() => {
    // If we have a new empty conversation but still have messages in state, clear them
    if (currentConversationId &&
        currentConversation &&
        currentConversation.messages.length === 0 &&
        messages.length > 0 &&
        !isTransitioning.current &&
        currentConversationId === lastSyncedConversationId.current) {
      console.log('Clearing stale messages for new conversation');
      setMessages([]);
      lastSyncedMessagesLength.current = 0;
    }
  }, [currentConversationId, currentConversation, messages.length, setMessages]);

  return (
    <div className="flex flex-col h-screen">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <h1 className="text-2xl font-bold text-gray-900">
          {currentConversation?.title || (currentConversationId ? 'New Chat' : 'AI Streaming Chat')}
        </h1>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length > 0 ? (
          messages.map(m => (
            <div 
              key={m.id} 
              className={`flex ${m.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div 
                className={`px-4 py-3 rounded-lg ${
                  m.role === 'user'
                    ? 'bg-blue-600 text-white max-w-xs lg:max-w-md mr-8'
                    : 'bg-gray-100 text-gray-900 max-w-[70%] ml-8'
                }`}
              >
                {m.role === 'user' ? (
                  <p className="whitespace-pre-wrap">{m.content}</p>
                ) : (
                  <div className="prose prose-sm max-w-none prose-gray">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeHighlight]}
                      components={{
                        code: ({ className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          return match ? (
                            <code className={className} {...props}>
                              {children}
                            </code>
                          ) : (
                            <code className="bg-gray-200 px-1 py-0.5 rounded text-sm" {...props}>
                              {children}
                            </code>
                          );
                        },
                        pre: ({ children, ...props }) => (
                          <pre className="bg-gray-800 text-gray-100 p-3 rounded-lg overflow-x-auto" {...props}>
                            {children}
                          </pre>
                        ),
                        h1: ({ children, ...props }) => (
                          <h1 className="text-xl font-bold mt-4 mb-2" {...props}>
                            {children}
                          </h1>
                        ),
                        h2: ({ children, ...props }) => (
                          <h2 className="text-lg font-semibold mt-3 mb-2" {...props}>
                            {children}
                          </h2>
                        ),
                        h3: ({ children, ...props }) => (
                          <h3 className="text-md font-semibold mt-2 mb-1" {...props}>
                            {children}
                          </h3>
                        ),
                        p: ({ children, ...props }) => (
                          <p className="mb-2 leading-relaxed" {...props}>
                            {children}
                          </p>
                        ),
                        ul: ({ children, ...props }) => (
                          <ul className="list-disc pl-4 mb-2 space-y-1" {...props}>
                            {children}
                          </ul>
                        ),
                        ol: ({ children, ...props }) => (
                          <ol className="list-decimal pl-4 mb-2 space-y-1" {...props}>
                            {children}
                          </ol>
                        ),
                        li: ({ children, ...props }) => (
                          <li className="leading-relaxed" {...props}>
                            {children}
                          </li>
                        ),
                        blockquote: ({ children, ...props }) => (
                          <blockquote className="border-l-4 border-gray-300 pl-4 italic my-2" {...props}>
                            {children}
                          </blockquote>
                        ),
                        table: ({ children, ...props }) => (
                          <div className="overflow-x-auto my-2">
                            <table className="min-w-full border-collapse border border-gray-300" {...props}>
                              {children}
                            </table>
                          </div>
                        ),
                        th: ({ children, ...props }) => (
                          <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-semibold" {...props}>
                            {children}
                          </th>
                        ),
                        td: ({ children, ...props }) => (
                          <td className="border border-gray-300 px-2 py-1" {...props}>
                            {children}
                          </td>
                        ),
                        a: ({ children, ...props }) => (
                          <a className="text-blue-600 hover:underline" {...props}>
                            {children}
                          </a>
                        ),
                        strong: ({ children, ...props }) => (
                          <strong className="font-semibold" {...props}>
                            {children}
                          </strong>
                        ),
                        em: ({ children, ...props }) => (
                          <em className="italic" {...props}>
                            {children}
                          </em>
                        )
                      }}
                    >
                      {m.content}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-12">
            <p className="text-lg">Start a conversation</p>
            <p className="text-sm">Type your message below to begin chatting with AI</p>
          </div>
        )}
        
        {/* Loading indicator */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-900 max-w-[70%] px-4 py-3 rounded-lg ml-8">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                </div>
                <span className="text-sm">AI is thinking...</span>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Chat Input Form */}
      <div className="border-t border-gray-200 p-4">
        <div className="max-w-4xl mx-auto">
          <form onSubmit={onSubmit} className="flex space-x-2">
            <textarea
              className="flex-1 px-4 py-3 h-20 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              value={input}
              placeholder="Type your message..."
              onChange={(e) => handleInputChange({ target: { value: e.target.value } } as React.ChangeEvent<HTMLInputElement>)}
              disabled={isLoading}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  if (input.trim() && !isLoading) {
                    onSubmit(e as any);
                  }
                }
              }}
            />
            <button
              type="submit"
              disabled={isLoading || !input.trim()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors self-end"
            >
              Send
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}