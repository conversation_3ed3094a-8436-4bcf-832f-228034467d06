export interface Conversation {
  id: string;
  title: string;
  messages: Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    createdAt: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ConversationContextType {
  conversations: Conversation[];
  currentConversationId: string | null;
  currentConversation: Conversation | null;
  createNewConversation: () => string;
  switchConversation: (id: string) => void;
  deleteConversation: (id: string) => void;
  updateConversation: (id: string, messages: Conversation['messages']) => void;
  generateConversationTitle: (messages: Conversation['messages']) => string;
}