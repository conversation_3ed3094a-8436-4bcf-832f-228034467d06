'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { Conversation, ConversationContextType } from '@/app/types/conversation';

const ConversationContext = createContext<ConversationContextType | null>(null);

export function useConversations() {
  const context = useContext(ConversationContext);
  if (!context) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
}

export function ConversationProvider({ children }: { children: ReactNode }) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  // Load conversations from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('conversations');
    const savedCurrentId = localStorage.getItem('currentConversationId');
    
    if (saved) {
      const parsedConversations = JSON.parse(saved).map((conv: Conversation) => ({
        ...conv,
        createdAt: new Date(conv.createdAt),
        updatedAt: new Date(conv.updatedAt),
        messages: conv.messages.map((msg: Conversation['messages'][0]) => ({
          ...msg,
          createdAt: new Date(msg.createdAt)
        }))
      }));
      setConversations(parsedConversations);
    }
    
    if (savedCurrentId) {
      setCurrentConversationId(savedCurrentId);
    }
  }, []);

  // Save conversations to localStorage whenever they change
  useEffect(() => {
    if (conversations.length > 0) {
      localStorage.setItem('conversations', JSON.stringify(conversations));
    }
  }, [conversations]);

  useEffect(() => {
    if (currentConversationId) {
      localStorage.setItem('currentConversationId', currentConversationId);
    }
  }, [currentConversationId]);

  const generateConversationTitle = useCallback((messages: Conversation['messages']): string => {
    if (messages.length === 0) return 'New Chat';
    
    const firstUserMessage = messages.find(m => m.role === 'user')?.content || '';
    if (firstUserMessage.length > 50) {
      return firstUserMessage.substring(0, 50) + '...';
    }
    return firstUserMessage || 'New Chat';
  }, []);

  const createNewConversation = useCallback((): string => {
    const id = Date.now().toString();
    const newConversation: Conversation = {
      id,
      title: 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Clear current conversation ID first to prevent race conditions
    setCurrentConversationId(null);

    // Add the new conversation
    setConversations(prev => [newConversation, ...prev]);

    // Set the new conversation as current after a brief delay to ensure clean state
    setTimeout(() => {
      setCurrentConversationId(id);
    }, 50);

    return id;
  }, []);

  const switchConversation = useCallback((id: string) => {
    // Clear current conversation first to prevent state leakage
    setCurrentConversationId(null);

    // Set new conversation after a brief delay to ensure clean state transition
    setTimeout(() => {
      setCurrentConversationId(id);
    }, 50);
  }, []);

  const deleteConversation = useCallback((id: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== id));
    
    if (currentConversationId === id) {
      setConversations(current => {
        const remaining = current.filter(conv => conv.id !== id);
        if (remaining.length > 0) {
          setCurrentConversationId(remaining[0].id);
        } else {
          setCurrentConversationId(null);
        }
        return remaining;
      });
    }
  }, [currentConversationId]);

  const updateConversation = useCallback((id: string, messages: Conversation['messages']) => {
    setConversations(prev => {
      const conversation = prev.find(conv => conv.id === id);
      if (!conversation) {
        console.warn(`Attempted to update non-existent conversation: ${id}`);
        return prev;
      }

      // Only update if we actually have new messages or different content
      const hasNewMessages = messages.length !== conversation.messages.length ||
        messages.some((msg, index) =>
          !conversation.messages[index] ||
          msg.content !== conversation.messages[index].content ||
          msg.id !== conversation.messages[index].id
        );

      if (!hasNewMessages) {
        return prev; // No changes needed
      }

      return prev.map(conv =>
        conv.id === id
          ? {
              ...conv,
              messages: [...messages], // Create a new array to prevent reference issues
              title: generateConversationTitle(messages),
              updatedAt: new Date()
            }
          : conv
      );
    });
  }, [generateConversationTitle]);

  const currentConversation = conversations.find(conv => conv.id === currentConversationId) || null;

  return (
    <ConversationContext.Provider value={{
      conversations,
      currentConversationId,
      currentConversation,
      createNewConversation,
      switchConversation,
      deleteConversation,
      updateConversation,
      generateConversationTitle
    }}>
      {children}
    </ConversationContext.Provider>
  );
}