# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Next.js 15 application with TypeScript that implements a streaming AI chat interface using OpenAI's GPT models. The application demonstrates real-time streaming responses using the Vercel AI SDK.

## Key Architecture

- **Next.js App Router**: Uses the modern App Router with server components
- **API Route**: `/app/api/chat/route.ts` - Edge runtime streaming API endpoint that interfaces with OpenAI
- **Client Component**: `/app/page.tsx` - React client component using `useChat` hook for real-time streaming
- **Streaming Implementation**: Uses `OpenAIStream` from the Vercel AI SDK for real-time response streaming

## Essential Commands

Development:
```bash
npm run dev --turbopack    # Start development server with Turbopack
npm run build              # Build for production
npm run start              # Start production server
npm run lint               # Run ESLint
```

## Key Dependencies

- `ai` (v4.3.16) - Vercel AI SDK for streaming chat functionality
- `openai` (v5.8.2) - OpenAI API client
- `next` (v15.3.5) - React framework
- `tailwindcss` (v4) - Styling

## Environment Variables

Required:
- `OPENAI_API_KEY` - OpenAI API key for chat completions

## TypeScript Configuration

- Uses path mapping with `@/*` for root-level imports
- Strict TypeScript configuration enabled
- Next.js TypeScript plugin configured

## Chat Implementation Details

The chat system uses a streaming architecture:
1. Client sends messages via POST to `/api/chat`
2. API route uses Edge runtime for optimal performance
3. OpenAI streaming response is processed via `OpenAIStream`
4. Client receives real-time updates through `useChat` hook

## Styling

- Uses Tailwind CSS v4 for styling
- Responsive design with mobile-first approach
- Dark/light theme ready (Tailwind classes in place)